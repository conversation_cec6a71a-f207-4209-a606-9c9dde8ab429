export default defineNitroPlugin((nitroApp) => {
  console.log('%c [ request ]-2', 'font-size:13px; background:#1df2c9; color:#61ffff;', );

  nitroApp.hooks.hook("request", (event) => {
    console.log('%c [请求时]-6', 'font-size:13px; background:#e4ffd2; color:#ffffff;', event.path);
  });

  nitroApp.hooks.hook("beforeResponse", (event) => {
    console.log("响应前", event.path);
  });

  nitroApp.hooks.hook("afterResponse", (event) => {
    console.log("响应后", event.path);
  });
})