export default defineEventHandler(async (event)=>{

  console.log('%c [ this is fetch api ]-3', 'font-size:13px; background:#5946a3; color:#9d8ae7;', );
  const body =await  readBody(event)
  console.log('%c [ body ]-5', 'font-size:13px; background:#38e64c; color:#7cff90;', body);

  let res ;
  try {
  res = await $fetch(`/api/posts/${body.id}`,)

  } catch (error:Error) {
    res = error.message
  }

  
  console.log('%c [  get Posts finished ]-14', 'font-size:13px; background:#cf253f; color:#ff6983;', res );
  return res
})