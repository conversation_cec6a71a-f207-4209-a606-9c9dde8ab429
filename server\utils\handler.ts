import type { EventHandler, EventHandlerRequest } from 'h3'

export const defineWrappedResponseHandler = <T extends EventHandlerRequest, D> (
  handler: EventHandler<T, D>
): EventHandler<T, D> =>
  defineEventHandler<T>(async event => {
    try {
      // 在路由处理器之前执行某些操作
      event.context.utils ={
        handler:true,
      }
      
      const response = await handler(event)
      // 在路由处理器之后执行操作


      console.log('%c [ 接口准备想要 ]-16', 'font-size:13px; background:#7a6606; color:#beaa4a;', );
      return { response }
    } catch (err) {
      // 错误处理
      return { err }
    }
  })
